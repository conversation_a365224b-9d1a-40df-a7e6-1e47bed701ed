import React, { ReactNode, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { 
  Menu, 
  X, 
  Sun, 
  Moon, 
  LogOut, 
  Bell,
  Search,
  User
} from 'lucide-react';
import Button from '../ui/Button';

interface MainLayoutProps {
  children: ReactNode;
  title: string;
  sidebar?: ReactNode;
  headerActions?: ReactNode;
}

export default function MainLayout({ 
  children, 
  title, 
  sidebar, 
  headerActions 
}: MainLayoutProps) {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-secondary-50 dark:bg-secondary-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      {sidebar && (
        <div className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-secondary-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="flex items-center justify-between h-16 px-4 border-b border-secondary-200 dark:border-secondary-700">
            <h2 className="text-lg font-semibold text-secondary-900 dark:text-white">
              Navigation
            </h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-2 rounded-md hover:bg-secondary-100 dark:hover:bg-secondary-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="flex-1 overflow-y-auto">
            {sidebar}
          </div>
        </div>
      )}

      {/* Main content */}
      <div className={`flex-1 ${sidebar ? 'lg:ml-64' : ''}`}>
        {/* Header */}
        <header className="bg-white dark:bg-secondary-800 shadow-sm border-b border-secondary-200 dark:border-secondary-700">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                {sidebar && (
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="lg:hidden p-2 rounded-md hover:bg-secondary-100 dark:hover:bg-secondary-700 mr-2"
                  >
                    <Menu className="w-5 h-5" />
                  </button>
                )}
                <h1 className="text-xl font-semibold text-secondary-900 dark:text-white">
                  {title}
                </h1>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* Search */}
                <div className="hidden md:block relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-secondary-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search..."
                    className="block w-full pl-10 pr-3 py-2 border border-secondary-300 rounded-md leading-5 bg-white dark:bg-secondary-700 dark:border-secondary-600 placeholder-secondary-500 focus:outline-none focus:placeholder-secondary-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  />
                </div>

                {/* Notifications */}
                <button className="p-2 rounded-md hover:bg-secondary-100 dark:hover:bg-secondary-700 relative">
                  <Bell className="w-5 h-5 text-secondary-600 dark:text-secondary-300" />
                  <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-error-500"></span>
                </button>

                {/* Theme Toggle */}
                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-md hover:bg-secondary-100 dark:hover:bg-secondary-700"
                  aria-label="Toggle theme"
                >
                  {theme === 'light' ? (
                    <Moon className="w-5 h-5 text-secondary-600 dark:text-secondary-300" />
                  ) : (
                    <Sun className="w-5 h-5 text-yellow-500" />
                  )}
                </button>

                {/* User Menu */}
                <div className="flex items-center space-x-3">
                  <div className="hidden md:block text-right">
                    <p className="text-sm font-medium text-secondary-900 dark:text-white">
                      {user?.name}
                    </p>
                    <p className="text-xs text-secondary-500 dark:text-secondary-400">
                      {user?.role === 'admin' ? 'Administrator' : 'Shop Manager'}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                  </div>
                </div>

                {/* Header Actions */}
                {headerActions}

                {/* Logout */}
                <Button
                  variant="error"
                  size="sm"
                  onClick={handleLogout}
                  leftIcon={<LogOut className="w-4 h-4" />}
                  className="hidden sm:flex"
                >
                  Logout
                </Button>
                <button
                  onClick={handleLogout}
                  className="sm:hidden p-2 rounded-md hover:bg-error-100 text-error-600"
                >
                  <LogOut className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1">
          <div className="px-4 sm:px-6 lg:px-8 py-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
