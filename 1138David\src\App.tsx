import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import LoginPage from './pages/LoginPage';
import AdminDashboard from './pages/AdminDashboard';
import ShopDashboard from './pages/ShopDashboard';

// Test component to verify everything is working
function TestPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">🎉 Application is Working!</h1>
        <p className="text-gray-600 mb-8">The Multi-Business Management System is now running successfully.</p>
        <div className="space-y-4">
          <div className="card p-6 max-w-md mx-auto">
            <h2 className="text-xl font-semibold mb-4">Test Login Credentials:</h2>
            <div className="space-y-2 text-left">
              <p><strong>Admin:</strong> David / David28</p>
              <p><strong>Shop A:</strong> ShopA / ShopA28</p>
              <p><strong>Shop B:</strong> ShopB / ShopB28</p>
              <p><strong>Shop C:</strong> ShopC / ShopC28</p>
            </div>
          </div>
          <a href="/login" className="btn-primary">Go to Login Page</a>
        </div>
      </div>
    </div>
  );
}

// Simple ProtectedRoute component
function ProtectedRoute({ children, requiredRole }: { children: React.ReactNode; requiredRole?: string | string[] }) {
  // For now, just return children - we'll add protection later
  return <>{children}</>;
}

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<LoginPage />} />

              {/* Protected Admin Routes */}
              <Route
                path="/admin/*"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <AdminDashboard />
                  </ProtectedRoute>
                }
              />

              {/* Protected Shop Routes */}
              <Route
                path="/shop/*"
                element={
                  <ProtectedRoute requiredRole={['shopA', 'shopB', 'shopC']}>
                    <ShopDashboard />
                  </ProtectedRoute>
                }
              />

              {/* Test page */}
              <Route path="/test" element={<TestPage />} />

              {/* Default redirect */}
              <Route path="/" element={<TestPage />} />

              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
