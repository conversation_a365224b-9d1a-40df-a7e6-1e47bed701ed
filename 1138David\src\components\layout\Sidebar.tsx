import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { 
  LayoutDashboard,
  Store,
  Users,
  Package,
  DollarSign,
  FileText,
  ClipboardCheck,
  Settings,
  ShoppingCart,
  TrendingUp,
  Building2
} from 'lucide-react';
import { UserRole } from '../../types';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: UserRole[];
}

const adminNavigation: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/admin/dashboard',
    icon: LayoutDashboard,
    roles: ['admin'],
  },
  {
    name: 'Shops',
    href: '/admin/shops',
    icon: Store,
    roles: ['admin'],
  },
  {
    name: 'Employees',
    href: '/admin/employees',
    icon: Users,
    roles: ['admin'],
  },
  {
    name: 'Inventory',
    href: '/admin/inventory',
    icon: Package,
    roles: ['admin'],
  },
  {
    name: 'Financial',
    href: '/admin/financial',
    icon: DollarSign,
    roles: ['admin'],
  },
  {
    name: 'Reports',
    href: '/admin/reports',
    icon: FileText,
    roles: ['admin'],
  },
  {
    name: 'Audits',
    href: '/admin/audits',
    icon: ClipboardCheck,
    roles: ['admin'],
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    roles: ['admin'],
  },
];

const shopNavigation: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/shop/dashboard',
    icon: LayoutDashboard,
    roles: ['shopA', 'shopB', 'shopC'],
  },
  {
    name: 'Sales',
    href: '/shop/sales',
    icon: ShoppingCart,
    roles: ['shopA', 'shopB', 'shopC'],
  },
  {
    name: 'Inventory',
    href: '/shop/inventory',
    icon: Package,
    roles: ['shopA', 'shopB', 'shopC'],
  },
  {
    name: 'Employees',
    href: '/shop/employees',
    icon: Users,
    roles: ['shopA', 'shopB', 'shopC'],
  },
  {
    name: 'Cash Register',
    href: '/shop/cash',
    icon: DollarSign,
    roles: ['shopA', 'shopB', 'shopC'],
  },
  {
    name: 'Expenses',
    href: '/shop/expenses',
    icon: TrendingUp,
    roles: ['shopA', 'shopB', 'shopC'],
  },
  {
    name: 'Reports',
    href: '/shop/reports',
    icon: FileText,
    roles: ['shopA', 'shopB', 'shopC'],
  },
];

export default function Sidebar() {
  const { user } = useAuth();

  if (!user) return null;

  const navigation = user.role === 'admin' ? adminNavigation : shopNavigation;
  const filteredNavigation = navigation.filter(item => 
    item.roles.includes(user.role)
  );

  return (
    <nav className="flex-1 px-4 py-6 space-y-2">
      {/* Logo/Brand */}
      <div className="flex items-center space-x-3 px-3 py-4 mb-6">
        <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <Building2 className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-sm font-semibold text-secondary-900 dark:text-white">
            Business Manager
          </h3>
          <p className="text-xs text-secondary-500 dark:text-secondary-400">
            {user.role === 'admin' ? 'Admin Panel' : 'Shop Panel'}
          </p>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="space-y-1">
        {filteredNavigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              `group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                isActive
                  ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400'
                  : 'text-secondary-600 hover:bg-secondary-100 hover:text-secondary-900 dark:text-secondary-300 dark:hover:bg-secondary-700 dark:hover:text-white'
              }`
            }
          >
            <item.icon
              className={`mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-200`}
              aria-hidden="true"
            />
            {item.name}
          </NavLink>
        ))}
      </div>

      {/* User Info */}
      <div className="mt-8 pt-6 border-t border-secondary-200 dark:border-secondary-700">
        <div className="px-3 py-2">
          <p className="text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
            Current User
          </p>
          <div className="mt-2">
            <p className="text-sm font-medium text-secondary-900 dark:text-white">
              {user.name}
            </p>
            <p className="text-xs text-secondary-500 dark:text-secondary-400">
              {user.role === 'admin' ? 'Administrator' : `${user.role.toUpperCase()} Manager`}
            </p>
          </div>
        </div>
      </div>

      {/* Quick Stats (for shop users) */}
      {user.role !== 'admin' && (
        <div className="mt-4 px-3">
          <div className="bg-secondary-50 dark:bg-secondary-700 rounded-lg p-3">
            <p className="text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider mb-2">
              Today's Summary
            </p>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-secondary-600 dark:text-secondary-300">Sales</span>
                <span className="text-xs font-medium text-secondary-900 dark:text-white">$1,234</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-secondary-600 dark:text-secondary-300">Cash</span>
                <span className="text-xs font-medium text-secondary-900 dark:text-white">$2,456</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-secondary-600 dark:text-secondary-300">Staff</span>
                <span className="text-xs font-medium text-secondary-900 dark:text-white">6/8</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
