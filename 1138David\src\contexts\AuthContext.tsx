import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

// Define types directly here to avoid import issues
export type UserRole = 'admin' | 'shopA' | 'shopB' | 'shopC';

export interface User {
  id: string;
  username: string;
  role: User<PERSON><PERSON>;
  shopId?: string;
  name: string;
  email?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  checkAuth: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock users data - In production, this would come from a backend
const mockUsers: Array<User & { password: string }> = [
  {
    id: '1',
    username: '<PERSON>',
    password: '<PERSON><PERSON>', // In production, this would be hashed
    role: 'admin',
    name: '<PERSON> (Admin)',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    username: 'ShopA',
    password: 'ShopA28',
    role: 'shopA',
    shopId: 'shop-a',
    name: 'Shop A Manager',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    username: 'ShopB',
    password: 'ShopB28',
    role: 'shopB',
    shopId: 'shop-b',
    name: 'Shop B Manager',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '4',
    username: 'ShopC',
    password: 'ShopC28',
    role: 'shopC',
    shopId: 'shop-c',
    name: 'Shop C Manager',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
];

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CHECK_AUTH'; payload: User | null };

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'CHECK_AUTH':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
      };
    default:
      return state;
  }
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Auto-logout after 30 minutes of inactivity
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const resetTimeout = () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (state.isAuthenticated) {
        timeoutId = setTimeout(() => {
          logout();
        }, 30 * 60 * 1000); // 30 minutes
      }
    };

    const handleActivity = () => {
      resetTimeout();
    };

    if (state.isAuthenticated) {
      resetTimeout();
      window.addEventListener('mousedown', handleActivity);
      window.addEventListener('keydown', handleActivity);
      window.addEventListener('scroll', handleActivity);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      window.removeEventListener('mousedown', handleActivity);
      window.removeEventListener('keydown', handleActivity);
      window.removeEventListener('scroll', handleActivity);
    };
  }, [state.isAuthenticated]);

  // Check for existing session on app load
  useEffect(() => {
    checkAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    dispatch({ type: 'LOGIN_START' });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const user = mockUsers.find(u => u.username === credentials.username);
      
      if (!user) {
        dispatch({ type: 'LOGIN_FAILURE', payload: 'User not found' });
        return false;
      }

      if (!user.isActive) {
        dispatch({ type: 'LOGIN_FAILURE', payload: 'Account is deactivated' });
        return false;
      }

      // In production, use proper password hashing
      if (user.password !== credentials.password) {
        dispatch({ type: 'LOGIN_FAILURE', payload: 'Invalid password' });
        return false;
      }

      const { password, ...userWithoutPassword } = user;
      const userWithLastLogin = {
        ...userWithoutPassword,
        lastLogin: new Date(),
      };

      // Store in localStorage for persistence
      localStorage.setItem('user', JSON.stringify(userWithLastLogin));
      localStorage.setItem('authToken', `token-${user.id}`);

      dispatch({ type: 'LOGIN_SUCCESS', payload: userWithLastLogin });
      return true;
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE', payload: 'Login failed. Please try again.' });
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('authToken');
    dispatch({ type: 'LOGOUT' });
  };

  const checkAuth = () => {
    const storedUser = localStorage.getItem('user');
    const authToken = localStorage.getItem('authToken');

    if (storedUser && authToken) {
      try {
        const user = JSON.parse(storedUser);
        dispatch({ type: 'CHECK_AUTH', payload: user });
      } catch (error) {
        localStorage.removeItem('user');
        localStorage.removeItem('authToken');
        dispatch({ type: 'CHECK_AUTH', payload: null });
      }
    } else {
      dispatch({ type: 'CHECK_AUTH', payload: null });
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
