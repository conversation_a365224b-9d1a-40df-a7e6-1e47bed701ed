import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import {
  Shop,
  Employee,
  InventoryItem,
  Transaction,
  Sale,
  Purchase,
  Audit,
  DailyCash,
  Attendance,
  StockMovement,
  DashboardStats,
  ShopPerformance
} from '../types';
import { 
  mockShops, 
  mockEmployees, 
  mockInventoryItems, 
  mockDailyCash, 
  mockAttendance, 
  mockSales,
  getAdminDashboardStats,
  getShopPerformance,
  getLowStockItems,
  calculateDailyStaffCosts
} from '../data/mockData';

interface DataState {
  shops: Shop[];
  employees: Employee[];
  inventory: InventoryItem[];
  dailyCash: DailyCash[];
  attendance: Attendance[];
  sales: Sale[];
  transactions: Transaction[];
  purchases: Purchase[];
  audits: Audit[];
  stockMovements: StockMovement[];
  isLoading: boolean;
  error: string | null;
}

interface DataContextType extends DataState {
  // Shop operations
  getShopById: (id: string) => Shop | undefined;
  getShopsByUser: (userId: string) => Shop[];
  
  // Employee operations
  getEmployeesByShop: (shopId: string) => Employee[];
  getActiveEmployees: (shopId?: string) => Employee[];
  addEmployee: (employee: Omit<Employee, 'id'>) => void;
  updateEmployee: (id: string, updates: Partial<Employee>) => void;
  
  // Inventory operations
  getInventoryByShop: (shopId: string) => InventoryItem[];
  getLowStockItemsByShop: (shopId?: string) => InventoryItem[];
  updateInventoryItem: (id: string, updates: Partial<InventoryItem>) => void;
  
  // Attendance operations
  getTodayAttendance: (shopId: string) => Attendance[];
  markAttendance: (attendance: Omit<Attendance, 'id'>) => void;
  
  // Sales operations
  addSale: (sale: Omit<Sale, 'id'>) => void;
  getSalesByShop: (shopId: string) => Sale[];
  
  // Cash operations
  getDailyCashByShop: (shopId: string) => DailyCash | undefined;
  updateDailyCash: (shopId: string, updates: Partial<DailyCash>) => void;
  
  // Dashboard data
  getAdminStats: () => DashboardStats;
  getShopPerformanceData: () => ShopPerformance[];
  
  // Utility functions
  calculateShopStaffCosts: (shopId: string) => number;
}

type DataAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_EMPLOYEE'; payload: Employee }
  | { type: 'UPDATE_EMPLOYEE'; payload: { id: string; updates: Partial<Employee> } }
  | { type: 'UPDATE_INVENTORY_ITEM'; payload: { id: string; updates: Partial<InventoryItem> } }
  | { type: 'MARK_ATTENDANCE'; payload: Attendance }
  | { type: 'ADD_SALE'; payload: Sale }
  | { type: 'UPDATE_DAILY_CASH'; payload: { shopId: string; updates: Partial<DailyCash> } };

const initialState: DataState = {
  shops: mockShops,
  employees: mockEmployees,
  inventory: mockInventoryItems,
  dailyCash: mockDailyCash,
  attendance: mockAttendance,
  sales: mockSales,
  transactions: [],
  purchases: [],
  audits: [],
  stockMovements: [],
  isLoading: false,
  error: null,
};

function dataReducer(state: DataState, action: DataAction): DataState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'ADD_EMPLOYEE':
      return {
        ...state,
        employees: [...state.employees, action.payload],
      };
    
    case 'UPDATE_EMPLOYEE':
      return {
        ...state,
        employees: state.employees.map(emp =>
          emp.id === action.payload.id
            ? { ...emp, ...action.payload.updates }
            : emp
        ),
      };
    
    case 'UPDATE_INVENTORY_ITEM':
      return {
        ...state,
        inventory: state.inventory.map(item =>
          item.id === action.payload.id
            ? { ...item, ...action.payload.updates, lastUpdated: new Date() }
            : item
        ),
      };
    
    case 'MARK_ATTENDANCE':
      return {
        ...state,
        attendance: [...state.attendance.filter(att => 
          !(att.employeeId === action.payload.employeeId && 
            att.date.toDateString() === action.payload.date.toDateString())
        ), action.payload],
      };
    
    case 'ADD_SALE':
      return {
        ...state,
        sales: [...state.sales, action.payload],
      };
    
    case 'UPDATE_DAILY_CASH':
      return {
        ...state,
        dailyCash: state.dailyCash.map(cash =>
          cash.shopId === action.payload.shopId
            ? { ...cash, ...action.payload.updates }
            : cash
        ),
      };
    
    default:
      return state;
  }
}

const DataContext = createContext<DataContextType | undefined>(undefined);

interface DataProviderProps {
  children: ReactNode;
}

export function DataProvider({ children }: DataProviderProps) {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('businessData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        // You could dispatch actions to restore the saved state here
        // For now, we'll use the mock data
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
  }, []);

  // Save data to localStorage when state changes
  useEffect(() => {
    const dataToSave = {
      employees: state.employees,
      inventory: state.inventory,
      dailyCash: state.dailyCash,
      attendance: state.attendance,
      sales: state.sales,
    };
    localStorage.setItem('businessData', JSON.stringify(dataToSave));
  }, [state.employees, state.inventory, state.dailyCash, state.attendance, state.sales]);

  // Shop operations
  const getShopById = (id: string) => state.shops.find(shop => shop.id === id);
  
  const getShopsByUser = (userId: string) => 
    state.shops.filter(shop => shop.managerId === userId);

  // Employee operations
  const getEmployeesByShop = (shopId: string) =>
    state.employees.filter(emp => emp.shopId === shopId);
  
  const getActiveEmployees = (shopId?: string) =>
    state.employees.filter(emp => emp.isActive && (!shopId || emp.shopId === shopId));
  
  const addEmployee = (employee: Omit<Employee, 'id'>) => {
    const newEmployee: Employee = {
      ...employee,
      id: `emp-${Date.now()}`,
    };
    dispatch({ type: 'ADD_EMPLOYEE', payload: newEmployee });
  };
  
  const updateEmployee = (id: string, updates: Partial<Employee>) => {
    dispatch({ type: 'UPDATE_EMPLOYEE', payload: { id, updates } });
  };

  // Inventory operations
  const getInventoryByShop = (shopId: string) =>
    state.inventory.filter(item => item.shopId === shopId);
  
  const getLowStockItemsByShop = (shopId?: string) =>
    getLowStockItems(shopId);
  
  const updateInventoryItem = (id: string, updates: Partial<InventoryItem>) => {
    dispatch({ type: 'UPDATE_INVENTORY_ITEM', payload: { id, updates } });
  };

  // Attendance operations
  const getTodayAttendance = (shopId: string) =>
    state.attendance.filter(att => 
      att.shopId === shopId && 
      att.date.toDateString() === new Date().toDateString()
    );
  
  const markAttendance = (attendance: Omit<Attendance, 'id'>) => {
    const newAttendance: Attendance = {
      ...attendance,
      id: `att-${Date.now()}`,
    };
    dispatch({ type: 'MARK_ATTENDANCE', payload: newAttendance });
  };

  // Sales operations
  const addSale = (sale: Omit<Sale, 'id'>) => {
    const newSale: Sale = {
      ...sale,
      id: `sale-${Date.now()}`,
    };
    dispatch({ type: 'ADD_SALE', payload: newSale });
  };
  
  const getSalesByShop = (shopId: string) =>
    state.sales.filter(sale => sale.shopId === shopId);

  // Cash operations
  const getDailyCashByShop = (shopId: string) =>
    state.dailyCash.find(cash => cash.shopId === shopId);
  
  const updateDailyCash = (shopId: string, updates: Partial<DailyCash>) => {
    dispatch({ type: 'UPDATE_DAILY_CASH', payload: { shopId, updates } });
  };

  // Dashboard data
  const getAdminStats = () => getAdminDashboardStats();
  const getShopPerformanceData = () => getShopPerformance();

  // Utility functions
  const calculateShopStaffCosts = (shopId: string) => calculateDailyStaffCosts(shopId);

  const value: DataContextType = {
    ...state,
    getShopById,
    getShopsByUser,
    getEmployeesByShop,
    getActiveEmployees,
    addEmployee,
    updateEmployee,
    getInventoryByShop,
    getLowStockItemsByShop,
    updateInventoryItem,
    getTodayAttendance,
    markAttendance,
    addSale,
    getSalesByShop,
    getDailyCashByShop,
    updateDailyCash,
    getAdminStats,
    getShopPerformanceData,
    calculateShopStaffCosts,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
}

export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}
