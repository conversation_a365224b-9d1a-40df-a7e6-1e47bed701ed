import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

// Simple function to get shop name from role
const getShopNameFromRole = (role: string): string => {
  const shopNames = {
    admin: 'All Shops',
    shopA: 'Shop A',
    shopB: 'Shop B',
    shopC: 'Shop C',
  };
  return shopNames[role as keyof typeof shopNames] || 'Unknown Shop';
};
import {
  Package,
  ShoppingCart,
  Users,
  DollarSign,
  Sun,
  Moon,
  LogOut
} from 'lucide-react';

export default function ShopDashboard() {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const shopName = user ? getShopNameFromRole(user.role) : 'Shop';

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {shopName} Dashboard
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                aria-label="Toggle theme"
              >
                {theme === 'light' ? (
                  <Moon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                ) : (
                  <Sun className="w-5 h-5 text-yellow-500" />
                )}
              </button>

              <div className="text-sm text-gray-600 dark:text-gray-300">
                Welcome, {user?.name}
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Routes>
          <Route path="/" element={<Navigate to="/shop/dashboard" replace />} />
          <Route path="/dashboard" element={<ShopDashboardHome shopName={shopName} />} />
          {/* Add more routes as we build them */}
        </Routes>
      </div>
    </div>
  );
}

function ShopDashboardHome({ shopName }: { shopName: string }) {
  const todayStats = [
    {
      name: "Today's Sales",
      value: '$1,234',
      change: '+8%',
      changeType: 'positive',
      icon: ShoppingCart,
    },
    {
      name: 'Cash in Hand',
      value: '$2,456',
      change: '+$234',
      changeType: 'positive',
      icon: DollarSign,
    },
    {
      name: 'Staff Present',
      value: '6/8',
      change: '2 absent',
      changeType: 'warning',
      icon: Users,
    },
    {
      name: 'Low Stock Items',
      value: '3',
      change: 'Need reorder',
      changeType: 'negative',
      icon: Package,
    },
  ];

  const quickActions = [
    { name: 'Record Sale', icon: ShoppingCart, href: '/shop/sales' },
    { name: 'Check Inventory', icon: Package, href: '/shop/inventory' },
    { name: 'Staff Attendance', icon: Users, href: '/shop/employees' },
    { name: 'Cash Register', icon: DollarSign, href: '/shop/cash' },
  ];

  const alerts = [
    {
      type: 'warning',
      message: 'Low stock: Product A (5 units remaining)',
      time: '10 minutes ago',
    },
    {
      type: 'info',
      message: 'Daily cash reconciliation pending',
      time: '1 hour ago',
    },
    {
      type: 'error',
      message: '2 employees marked absent today',
      time: '2 hours ago',
    },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {shopName} Operations
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Manage your daily operations, track sales, and monitor inventory.
        </p>
      </div>

      {/* Today's Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {todayStats.map((stat) => (
          <div key={stat.name} className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.name}
                </p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p className={`ml-2 text-sm font-medium ${
                    stat.changeType === 'positive'
                      ? 'text-green-600'
                      : stat.changeType === 'negative'
                      ? 'text-red-600'
                      : stat.changeType === 'warning'
                      ? 'text-yellow-600'
                      : 'text-gray-500'
                  }`}>
                    {stat.change}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <button
              key={action.name}
              className="card p-4 hover:shadow-lg transition-shadow duration-200 text-center"
            >
              <action.icon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {action.name}
              </p>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
