import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import {
  Store,
  Users,
  Package,
  DollarSign,
  Sun,
  Moon,
  LogOut
} from 'lucide-react';

export default function AdminDashboard() {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Admin Dashboard
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                aria-label="Toggle theme"
              >
                {theme === 'light' ? (
                  <Moon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                ) : (
                  <Sun className="w-5 h-5 text-yellow-500" />
                )}
              </button>

              <div className="text-sm text-gray-600 dark:text-gray-300">
                Welcome, {user?.name}
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Routes>
          <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="/dashboard" element={<AdminDashboardHome />} />
          {/* Add more routes as we build them */}
        </Routes>
      </div>
    </div>
  );
}

function AdminDashboardHome() {
  const stats = [
    {
      name: 'Total Revenue',
      value: '$45,231',
      change: '+12%',
      changeType: 'positive',
      icon: DollarSign,
    },
    {
      name: 'Active Shops',
      value: '3',
      change: '0%',
      changeType: 'neutral',
      icon: Store,
    },
    {
      name: 'Total Employees',
      value: '24',
      change: '+2',
      changeType: 'positive',
      icon: Users,
    },
    {
      name: 'Low Stock Items',
      value: '12',
      change: '-3',
      changeType: 'negative',
      icon: Package,
    },
  ];

  const quickActions = [
    { name: 'Manage Shops', icon: Store, href: '/admin/shops' },
    { name: 'Employee Management', icon: Users, href: '/admin/employees' },
    { name: 'Inventory Overview', icon: Package, href: '/admin/inventory' },
    { name: 'Financial Summary', icon: DollarSign, href: '/admin/financial' },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Multi-Business Overview
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Monitor and manage all your retail businesses from one central dashboard.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.name}
                </p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p className={`ml-2 text-sm font-medium ${
                    stat.changeType === 'positive'
                      ? 'text-green-600'
                      : stat.changeType === 'negative'
                      ? 'text-red-600'
                      : 'text-gray-500'
                  }`}>
                    {stat.change}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <button
              key={action.name}
              className="card p-4 hover:shadow-lg transition-shadow duration-200 text-center"
            >
              <action.icon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {action.name}
              </p>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
