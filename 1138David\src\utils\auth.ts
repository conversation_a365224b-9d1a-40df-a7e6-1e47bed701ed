// Define UserRole type directly here to avoid import issues
export type UserRole = 'admin' | 'shopA' | 'shopB' | 'shopC';

// Simple password validation (no bcrypt for now to avoid issues)
export const hashPassword = async (password: string): Promise<string> => {
  return password; // For demo purposes, return plain password
};

export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return password === hashedPassword; // Simple comparison for demo
};

// Role-based access control
export const hasPermission = (userRole: UserRole, requiredRole: UserRole | UserRole[]): boolean => {
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(userRole);
  }
  
  // Admin has access to everything
  if (userRole === 'admin') {
    return true;
  }
  
  // Shop managers only have access to their own role
  return userRole === requiredRole;
};

// Route permissions
export const getRoutePermissions = (userRole: UserRole) => {
  const permissions = {
    admin: [
      '/admin',
      '/admin/dashboard',
      '/admin/shops',
      '/admin/employees',
      '/admin/inventory',
      '/admin/financial',
      '/admin/reports',
      '/admin/audits',
      '/admin/settings',
    ],
    shopA: [
      '/shop/dashboard',
      '/shop/inventory',
      '/shop/sales',
      '/shop/employees',
      '/shop/cash',
      '/shop/reports',
    ],
    shopB: [
      '/shop/dashboard',
      '/shop/inventory',
      '/shop/sales',
      '/shop/employees',
      '/shop/cash',
      '/shop/reports',
    ],
    shopC: [
      '/shop/dashboard',
      '/shop/inventory',
      '/shop/sales',
      '/shop/employees',
      '/shop/cash',
      '/shop/reports',
    ],
  };

  return permissions[userRole] || [];
};

// Get default redirect path based on user role
export const getDefaultRedirectPath = (userRole: UserRole): string => {
  const redirectPaths = {
    admin: '/admin/dashboard',
    shopA: '/shop/dashboard',
    shopB: '/shop/dashboard',
    shopC: '/shop/dashboard',
  };

  return redirectPaths[userRole] || '/';
};

// Validate password strength
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Generate secure token
export const generateToken = (): string => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

// Check if token is expired
export const isTokenExpired = (tokenTimestamp: number, expirationHours: number = 24): boolean => {
  const now = Date.now();
  const expirationTime = tokenTimestamp + (expirationHours * 60 * 60 * 1000);
  return now > expirationTime;
};

// Format user display name
export const formatUserDisplayName = (name: string, role: UserRole): string => {
  const roleLabels = {
    admin: 'Administrator',
    shopA: 'Shop A Manager',
    shopB: 'Shop B Manager',
    shopC: 'Shop C Manager',
  };
  
  return `${name} (${roleLabels[role]})`;
};

// Get shop name from role
export const getShopNameFromRole = (role: UserRole): string => {
  const shopNames = {
    admin: 'All Shops',
    shopA: 'Shop A',
    shopB: 'Shop B',
    shopC: 'Shop C',
  };
  
  return shopNames[role] || 'Unknown Shop';
};
