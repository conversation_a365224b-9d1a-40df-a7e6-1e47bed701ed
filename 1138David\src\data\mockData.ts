import { 
  Shop, 
  Employee, 
  InventoryItem, 
  Transaction, 
  Sale, 
  Purchase, 
  Audit,
  DailyCash,
  Attendance,
  StockMovement
} from '../types';

// Mock Shops Data
export const mockShops: Shop[] = [
  {
    id: 'shop-a',
    name: 'Shop A - Downtown',
    code: 'SHOP_A',
    address: '123 Main Street, Downtown',
    phone: '******-0101',
    managerId: '2',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'shop-b',
    name: 'Shop B - Mall',
    code: 'SHOP_B',
    address: '456 Mall Avenue, Shopping Center',
    phone: '******-0102',
    managerId: '3',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'shop-c',
    name: 'Shop C - Suburb',
    code: 'SHOP_C',
    address: '789 Suburb Road, Residential Area',
    phone: '******-0103',
    managerId: '4',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
];

// Mock Employees Data
export const mockEmployees: Employee[] = [
  // Shop A Employees
  {
    id: 'emp-1',
    name: '<PERSON>',
    shopId: 'shop-a',
    position: 'Sales Associate',
    dailyWage: 80,
    isActive: true,
    hireDate: new Date('2024-01-15'),
    phone: '******-1001',
  },
  {
    id: 'emp-2',
    name: 'Sarah Johnson',
    shopId: 'shop-a',
    position: 'Cashier',
    dailyWage: 75,
    isActive: true,
    hireDate: new Date('2024-02-01'),
    phone: '******-1002',
  },
  {
    id: 'emp-3',
    name: 'Mike Wilson',
    shopId: 'shop-a',
    position: 'Stock Clerk',
    dailyWage: 70,
    isActive: true,
    hireDate: new Date('2024-02-15'),
    phone: '******-1003',
  },
  // Shop B Employees
  {
    id: 'emp-4',
    name: 'Emily Davis',
    shopId: 'shop-b',
    position: 'Sales Associate',
    dailyWage: 80,
    isActive: true,
    hireDate: new Date('2024-01-20'),
    phone: '******-2001',
  },
  {
    id: 'emp-5',
    name: 'David Brown',
    shopId: 'shop-b',
    position: 'Cashier',
    dailyWage: 75,
    isActive: true,
    hireDate: new Date('2024-02-05'),
    phone: '******-2002',
  },
  {
    id: 'emp-6',
    name: 'Lisa Garcia',
    shopId: 'shop-b',
    position: 'Stock Clerk',
    dailyWage: 70,
    isActive: false,
    hireDate: new Date('2024-01-10'),
    phone: '******-2003',
  },
  // Shop C Employees
  {
    id: 'emp-7',
    name: 'Robert Taylor',
    shopId: 'shop-c',
    position: 'Sales Associate',
    dailyWage: 80,
    isActive: true,
    hireDate: new Date('2024-01-25'),
    phone: '******-3001',
  },
  {
    id: 'emp-8',
    name: 'Jennifer Martinez',
    shopId: 'shop-c',
    position: 'Cashier',
    dailyWage: 75,
    isActive: true,
    hireDate: new Date('2024-02-10'),
    phone: '******-3002',
  },
];

// Mock Inventory Items
export const mockInventoryItems: InventoryItem[] = [
  // Shop A Inventory
  {
    id: 'item-1',
    name: 'Product A',
    category: 'Electronics',
    shopId: 'shop-a',
    quantity: 25,
    unitCost: 50,
    sellingPrice: 75,
    minStockLevel: 10,
    supplier: 'Supplier 1',
    lastUpdated: new Date(),
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'item-2',
    name: 'Product B',
    category: 'Clothing',
    shopId: 'shop-a',
    quantity: 5,
    unitCost: 20,
    sellingPrice: 35,
    minStockLevel: 15,
    supplier: 'Supplier 2',
    lastUpdated: new Date(),
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'item-3',
    name: 'Product C',
    category: 'Home & Garden',
    shopId: 'shop-a',
    quantity: 40,
    unitCost: 15,
    sellingPrice: 25,
    minStockLevel: 20,
    supplier: 'Supplier 3',
    lastUpdated: new Date(),
    createdAt: new Date('2024-01-01'),
  },
  // Shop B Inventory
  {
    id: 'item-4',
    name: 'Product D',
    category: 'Electronics',
    shopId: 'shop-b',
    quantity: 30,
    unitCost: 45,
    sellingPrice: 70,
    minStockLevel: 12,
    supplier: 'Supplier 1',
    lastUpdated: new Date(),
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'item-5',
    name: 'Product E',
    category: 'Sports',
    shopId: 'shop-b',
    quantity: 8,
    unitCost: 30,
    sellingPrice: 50,
    minStockLevel: 10,
    supplier: 'Supplier 4',
    lastUpdated: new Date(),
    createdAt: new Date('2024-01-01'),
  },
  // Shop C Inventory
  {
    id: 'item-6',
    name: 'Product F',
    category: 'Books',
    shopId: 'shop-c',
    quantity: 60,
    unitCost: 8,
    sellingPrice: 15,
    minStockLevel: 25,
    supplier: 'Supplier 5',
    lastUpdated: new Date(),
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'item-7',
    name: 'Product G',
    category: 'Toys',
    shopId: 'shop-c',
    quantity: 3,
    unitCost: 12,
    sellingPrice: 20,
    minStockLevel: 8,
    supplier: 'Supplier 6',
    lastUpdated: new Date(),
    createdAt: new Date('2024-01-01'),
  },
];

// Mock Daily Cash Records
export const mockDailyCash: DailyCash[] = [
  {
    id: 'cash-1',
    shopId: 'shop-a',
    date: new Date(),
    openingBalance: 500,
    closingBalance: 1234,
    totalSales: 1200,
    totalExpenses: 300,
    staffCosts: 225,
    physicalCount: 1230,
    variance: -4,
    userId: '2',
  },
  {
    id: 'cash-2',
    shopId: 'shop-b',
    date: new Date(),
    openingBalance: 600,
    closingBalance: 1456,
    totalSales: 1100,
    totalExpenses: 244,
    staffCosts: 150,
    physicalCount: 1456,
    variance: 0,
    userId: '3',
  },
  {
    id: 'cash-3',
    shopId: 'shop-c',
    date: new Date(),
    openingBalance: 400,
    closingBalance: 987,
    totalSales: 800,
    totalExpenses: 213,
    staffCosts: 155,
    physicalCount: 990,
    variance: 3,
    userId: '4',
  },
];

// Mock Attendance Records
export const mockAttendance: Attendance[] = [
  // Today's attendance
  {
    id: 'att-1',
    employeeId: 'emp-1',
    shopId: 'shop-a',
    date: new Date(),
    status: 'present',
    hoursWorked: 8,
  },
  {
    id: 'att-2',
    employeeId: 'emp-2',
    shopId: 'shop-a',
    date: new Date(),
    status: 'present',
    hoursWorked: 8,
  },
  {
    id: 'att-3',
    employeeId: 'emp-3',
    shopId: 'shop-a',
    date: new Date(),
    status: 'absent',
    hoursWorked: 0,
  },
  {
    id: 'att-4',
    employeeId: 'emp-4',
    shopId: 'shop-b',
    date: new Date(),
    status: 'present',
    hoursWorked: 8,
  },
  {
    id: 'att-5',
    employeeId: 'emp-5',
    shopId: 'shop-b',
    date: new Date(),
    status: 'half-day',
    hoursWorked: 4,
  },
  {
    id: 'att-6',
    employeeId: 'emp-7',
    shopId: 'shop-c',
    date: new Date(),
    status: 'present',
    hoursWorked: 8,
  },
  {
    id: 'att-7',
    employeeId: 'emp-8',
    shopId: 'shop-c',
    date: new Date(),
    status: 'present',
    hoursWorked: 8,
  },
];

// Mock Recent Sales
export const mockSales: Sale[] = [
  {
    id: 'sale-1',
    shopId: 'shop-a',
    items: [
      { itemId: 'item-1', quantity: 2, unitPrice: 75, totalPrice: 150 },
      { itemId: 'item-3', quantity: 1, unitPrice: 25, totalPrice: 25 },
    ],
    totalAmount: 175,
    paymentMethod: 'cash',
    date: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    userId: '2',
    receiptNumber: 'RCP-001',
  },
  {
    id: 'sale-2',
    shopId: 'shop-b',
    items: [
      { itemId: 'item-4', quantity: 1, unitPrice: 70, totalPrice: 70 },
    ],
    totalAmount: 70,
    paymentMethod: 'card',
    date: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    userId: '3',
    receiptNumber: 'RCP-002',
  },
];

// Helper function to get data by shop
export const getDataByShop = (shopId: string) => {
  return {
    employees: mockEmployees.filter(emp => emp.shopId === shopId),
    inventory: mockInventoryItems.filter(item => item.shopId === shopId),
    dailyCash: mockDailyCash.find(cash => cash.shopId === shopId),
    attendance: mockAttendance.filter(att => att.shopId === shopId),
    sales: mockSales.filter(sale => sale.shopId === shopId),
  };
};

// Helper function to get low stock items
export const getLowStockItems = (shopId?: string) => {
  const items = shopId 
    ? mockInventoryItems.filter(item => item.shopId === shopId)
    : mockInventoryItems;
  
  return items.filter(item => item.quantity <= item.minStockLevel);
};

// Helper function to calculate daily staff costs
export const calculateDailyStaffCosts = (shopId: string) => {
  const shopEmployees = mockEmployees.filter(emp => emp.shopId === shopId && emp.isActive);
  const todayAttendance = mockAttendance.filter(att =>
    att.shopId === shopId &&
    att.date.toDateString() === new Date().toDateString()
  );

  let totalCost = 0;
  shopEmployees.forEach(employee => {
    const attendance = todayAttendance.find(att => att.employeeId === employee.id);
    if (attendance) {
      switch (attendance.status) {
        case 'present':
          totalCost += employee.dailyWage;
          break;
        case 'half-day':
          totalCost += employee.dailyWage * 0.5;
          break;
        case 'overtime':
          totalCost += employee.dailyWage * 1.5;
          break;
        // absent = no cost
      }
    }
  });

  return totalCost;
};

// Helper function to get dashboard stats for admin
export const getAdminDashboardStats = () => {
  const totalRevenue = mockDailyCash.reduce((sum, cash) => sum + cash.totalSales, 0);
  const totalExpenses = mockDailyCash.reduce((sum, cash) => sum + cash.totalExpenses + cash.staffCosts, 0);
  const netProfit = totalRevenue - totalExpenses;
  const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

  return {
    totalRevenue,
    totalExpenses,
    netProfit,
    profitMargin,
    totalEmployees: mockEmployees.filter(emp => emp.isActive).length,
    activeShops: mockShops.filter(shop => shop.isActive).length,
    lowStockItems: getLowStockItems().length,
    pendingAudits: 0, // Will be implemented later
  };
};

// Helper function to get shop performance data
export const getShopPerformance = () => {
  return mockShops.map(shop => {
    const shopCash = mockDailyCash.find(cash => cash.shopId === shop.id);
    const revenue = shopCash?.totalSales || 0;
    const expenses = (shopCash?.totalExpenses || 0) + (shopCash?.staffCosts || 0);
    const profit = revenue - expenses;
    const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0;
    const employeeCount = mockEmployees.filter(emp => emp.shopId === shop.id && emp.isActive).length;
    const inventoryValue = mockInventoryItems
      .filter(item => item.shopId === shop.id)
      .reduce((sum, item) => sum + (item.quantity * item.unitCost), 0);

    return {
      shopId: shop.id,
      shopName: shop.name,
      revenue,
      expenses,
      profit,
      profitMargin,
      employeeCount,
      inventoryValue,
    };
  });
};
